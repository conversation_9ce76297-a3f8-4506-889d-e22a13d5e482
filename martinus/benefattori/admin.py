from django.utils.translation import ugettext_lazy as _
from django.contrib.admin.filters import RelatedOnlyFieldList<PERSON>ilter

from import_export.admin import ImportMixin

from matthaeus.anagrafica.admin import AnagraficaAdmin
from martinus.adesioni.admin import AdozioneADistanzaInline, SostegnoProgettoInline
from martinus.adesioni.admin import ContributoOccasionaleInline, ContributoPeriodicoInline
from martinus.admin import MartinusModelAdmin
from martinus.donazioni.admin import DonazioneInline
from martinus.benefattori.resources import BenefattoreImportResource
from publius.persone.admin import MagisterMultipleListFilter
from matthaeus.anagrafica.models import Tipologia, TipoComunicazione


class TipologiaMultipleListFilter(MagisterMultipleListFilter):
    title = _('Tipologia')
    parameter_name = 'tipologia__in'

    def lookups(self, request, model_admin):
        elenco_tipologie_filtro = []
        pk_qs = model_admin.get_queryset(request).values('tipologia').distinct()
        elenco_tipologie = Tipologia.objects.filter(pk__in=pk_qs)
        for tipologia in elenco_tipologie:
            elenco_tipologie_filtro.append((tipologia.id, '%s' % tipologia))
        return elenco_tipologie_filtro
        

class TipoComunicazioneMultipleListFilter(MagisterMultipleListFilter):
    title = _('Tipo Comunicazione')
    parameter_name = 'tipo_comunicazione__in'

    def lookups(self, request, model_admin):
        elenco_tipologie_filtro = []
        pk_qs = model_admin.get_queryset(request).values('tipo_comunicazione').distinct()
        elenco_tipologie = TipoComunicazione.objects.filter(pk__in=pk_qs)
        for tipo_comunicazione in elenco_tipologie:
            elenco_tipologie_filtro.append((tipo_comunicazione.id, '%s' % tipo_comunicazione))
        return elenco_tipologie_filtro


class BenefattoreAdmin(ImportMixin, MartinusModelAdmin):
    list_display = ('ragione_sociale', 'indirizzo', 'citta', 'telefono', 'email', 'data_nascita', 'note')
    list_filter = (
        'attivo',
        ('stato', RelatedOnlyFieldListFilter),
        ('provincia', RelatedOnlyFieldListFilter),
        TipologiaMultipleListFilter,
        TipoComunicazioneMultipleListFilter,
    )
    autocomplete_fields = ('provincia', 'stato', 'tipologia', 'tipo_comunicazione')
    resource_class = BenefattoreImportResource
    suit_list_filter_horizontal = ('provincia', 'stato')
    search_fields = ('ragione_sociale', 'indirizzo', 'citta', 'telefono', 'email', 'note')
    fieldsets = (
        (
            None, dict(
                classes=('suit-tab', 'suit-tab-dati_anagrafici'),
                fields=(
                    'ragione_sociale', 
                    'appellativo',
                    'data_nascita',
                    'codice', 
                    'tipologia',
                )
            )
        ),
        (
            _('Dati Anagrafici'), dict(
                classes=('suit-tab', 'suit-tab-dati_anagrafici'),
                fields=(
                    'presso',
                    'indirizzo', 
                    'citta', 
                    'provincia', 
                    'regione',
                    'cap',
                    'stato',
                )
            )
        ),
        (
            _('Dati Contatto'), dict(
                classes=('suit-tab', 'suit-tab-dati_anagrafici'),
                fields=(
                    'tipo_comunicazione',
                    'telefono',
                    'telefono_2',
                    'cellulare',
                    'fax',
                    'email',
                    'pec',
                )
            )
        ),
        (
            _('Dati Fiscali'), dict(
                classes=('suit-tab', 'suit-tab-dati_anagrafici'),
                fields=(
                    'partita_iva',
                    'codice_fiscale',
                    'codice_sdi',
                )
            )
        ),
        (
            _('Dati Consacrazione'), dict(
                classes=('suit-tab', 'suit-tab-dati_anagrafici'),
                fields=(
                    'consacrato',
                    'data_consacrazione',
                    'luogo_consacrazione',
                    'nome_padrino',
                )
            )
        ),
        (
            _('Note'), dict(
                classes=('suit-tab', 'suit-tab-dati_anagrafici'),
                fields=(
                    'note',
                )
            )
        ),
    )
    # aggiungi delle tabs di suite per visualizzare le adozioni a distanza, i progetti, i contributi occasionali e i contributi ricorrenti
    suit_form_tabs = (
        ('dati_anagrafici', _('Dati Anagrafici')),
        ('sostegno_progetti', _('Sostegno a Progetti')),
        ('adozioni_distanza', _('Adozioni a Distanza')),
        ('contributi_occasionali', _('Contributi Occasionali')),
        ('contributi_periodici', _('Contributi Periodici')),
        ('donazioni', _('Donazioni')),
    )
    # importa e aggiungi inline per visualizzare le adozioni a distanza, i progetti, i contributi occasionali e i contributi ricorrenti
    inlines = [
        AdozioneADistanzaInline, SostegnoProgettoInline, 
        ContributoOccasionaleInline, ContributoPeriodicoInline,
        DonazioneInline,
    ]

